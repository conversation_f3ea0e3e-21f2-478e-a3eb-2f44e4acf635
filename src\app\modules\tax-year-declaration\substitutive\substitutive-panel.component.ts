import { Component, Input, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import {
  ShowSubstitutivePanelInfo,
  Substitutiva,
} from '../tax-year-declaration.model';
import { TaxYearDeclarationService } from '../services/tax-declaration.service';
import { Column, Row, SeAlertType } from 'se-ui-components-mf-lib';
import { TaxYearDeclarationEndpointService } from '../services/tax-declaration-endpoint.service';
import { StoreService } from '@core/services';

@Component({
  selector: 'app-substitutive-panel',
  templateUrl: './substitutive-panel.component.html',
  styleUrls: [],
})
export class SubstitutivePanelComponent {
  @Input() set taxYear(value: string | null) {
    if (value) {
      this.alertInfo = this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.ALERT_INFO',
        { taxYear: value },
      );
    }
  }
  @Input() set model(value: string | null) {
    if (value) {
      // Añadir validacion
      
    }
  }
  @Input() set substitutiva(value: ShowSubstitutivePanelInfo) {
    this._substitutiveData = value;
    if (!value.showPanel) {
      this.resetData();
      return;
    } else if (value.substitutive) {
      this.tableData = [
        {
          data: {
            numJustificant: { value: value.substitutive.numJustificant },
            declarant: { value: value.substitutive.declarant },
            dataPresentacio: { value: value.substitutive.dataPresentacio },
            estat: { value: value.substitutive.estat },
          },
        },
      ];
    } else if (!value.hasDeclaracioPrevia) {
      this.showDifferentPresenterForm = false;
      this.componentForm.get('substitutiva')?.setValue(false);
    }
  }

  @Output() substitutiveChange: Subject<Substitutiva | null> =
    new Subject<Substitutiva | null>();

  protected alertInfo: string = this.translateService.instant(
    'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.ALERT_INFO',
  );
  protected tableColumns: Column[] =
    this.taxDeclarationService.getSubstitutiveTableColumns();
  protected _substitutiveData: ShowSubstitutivePanelInfo | undefined;
  protected tableData: Row[] = [];
  protected componentForm: FormGroup;
  protected showDifferentPresenterForm: boolean = true;
  protected showValidateResultMessage: {
    type: SeAlertType.SUCCESS | SeAlertType.ERROR;
    message: string;
  } | null = null;

  constructor(
    private translateService: TranslateService,
    private fb: FormBuilder,
    private taxDeclarationService: TaxYearDeclarationService,
    private taxDeclarationEndpointService: TaxYearDeclarationEndpointService,
    private store: StoreService,
  ) {
    this.componentForm = this.fb.group({
      substitutiva: [true],
      numJustificant: [''],
      dataPresentacio: [''],
    });
  }

  protected onSubstitutiveChange(event: boolean): void {
    if (!event) {
      this.substitutiveChange.next(null);
    } else {
      this.substitutiveChange.next(
        this._substitutiveData?.substitutive || null,
      );
    }
  }

  protected openDifferentPresenterForm(event: boolean): void {
    this.showDifferentPresenterForm = event;
    this.componentForm.get('substitutiva')?.setValue(event);
    if (!event) {
      this.componentForm.reset();
      this.showValidateResultMessage = null;
    }
  }

  private resetData(): void {
    this.tableData = [];
    this.showValidateResultMessage = null;
    this.showDifferentPresenterForm = false;
    this.componentForm.reset();
  }

  protected isButtonDisabled(): boolean {
    const numJustificantValue = this.componentForm.get('numJustificant')?.value;
    const dataPresentacioValue =
      this.componentForm.get('dataPresentacio')?.value;

    return !numJustificantValue || !dataPresentacioValue;
  }

  protected validateDifferentPresenterData(): void {
    const numJustificantValue = this.componentForm.get('numJustificant')?.value;
    const dataPresentacioValue =
      this.componentForm.get('dataPresentacio')?.value;

    this.taxDeclarationEndpointService
      .searchManualSubstitutiva(
        this.store.idTramit!,
        numJustificantValue,
        dataPresentacioValue,
      )
      .subscribe((response) => {
        this.substitutiveChange.next(response.content as Substitutiva);
        if (response.content?.numJustificant) {
          this.showValidateResultMessage = {
            type: SeAlertType.SUCCESS,
            message: this.translateService.instant(
              'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.DIFFERENT_PRESENTER.VALIDATE_SUCCESS_MESSAGE',
            ),
          };
        } else {
          this.showValidateResultMessage = {
            type: SeAlertType.ERROR,
            message: this.translateService.instant(
              'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.DIFFERENT_PRESENTER.VALIDATE_ERROR_MESSAGE',
            ),
          };
        }
      });
  }

  private validateJustificantNumber(expectedStart: string): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const value = control.value;

      // Si no hay valor, no validar (permitir campos vacíos)
      if (!value || value.trim() === '') {
        return null;
      }

      // Convertir a string y limpiar espacios
      const cleanValue = String(value).trim();

      // Verificar longitud total
      if (cleanValue.length !== 13) {
        return {
          invalidLength: {
            message: `El justificante debe contener exactamente 13 caracteres (actual: ${cleanValue.length})`,
            expectedLength: 13,
            actualLength: cleanValue.length
          }
        };
      }

      // Verificar que comience con los números esperados
      const actualStart = cleanValue.substring(0, 3);

      if (actualStart !== expectedStart) {
        return {
          invalidStart: {
            message: `El justificante debe comenzar con ${expectedStart} (actual: ${actualStart})`,
            expectedStart: expectedStart,
            actualStart: actualStart
          }
        };
      }

      return null;
    };
  }
}
