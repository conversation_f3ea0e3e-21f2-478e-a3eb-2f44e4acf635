import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SubstitutivePanelComponent } from './substitutive-panel.component';
import { TaxYearDeclarationService } from '../services/tax-declaration.service';
import { TaxYearDeclarationEndpointService } from '../services/tax-declaration-endpoint.service';
import { StoreService } from '@core/services';
import { of } from 'rxjs';

describe('SubstitutivePanelComponent - Validación numJustificant', () => {
  let component: SubstitutivePanelComponent;
  let fixture: ComponentFixture<SubstitutivePanelComponent>;
  let translateService: jasmine.SpyObj<TranslateService>;

  beforeEach(async () => {
    const translateSpy = jasmine.createSpyObj('TranslateService', ['instant']);
    const taxDeclarationServiceSpy = jasmine.createSpyObj('TaxYearDeclarationService', ['getSubstitutiveTableColumns']);
    const taxDeclarationEndpointServiceSpy = jasmine.createSpyObj('TaxYearDeclarationEndpointService', ['searchManualSubstitutiva']);
    const storeServiceSpy = jasmine.createSpyObj('StoreService', [], { idTramit: 'test-id' });

    await TestBed.configureTestingModule({
      declarations: [SubstitutivePanelComponent],
      imports: [ReactiveFormsModule, TranslateModule.forRoot()],
      providers: [
        FormBuilder,
        { provide: TranslateService, useValue: translateSpy },
        { provide: TaxYearDeclarationService, useValue: taxDeclarationServiceSpy },
        { provide: TaxYearDeclarationEndpointService, useValue: taxDeclarationEndpointServiceSpy },
        { provide: StoreService, useValue: storeServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SubstitutivePanelComponent);
    component = fixture.componentInstance;
    translateService = TestBed.inject(TranslateService) as jasmine.SpyObj<TranslateService>;
    
    // Mock de las traducciones
    translateService.instant.and.returnValue('Mensaje de error mock');
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Validación del campo numJustificant', () => {
    beforeEach(() => {
      // Simular que se establece un modelo
      component.model = '123';
    });

    it('debe permitir campos vacíos', () => {
      const control = component.componentForm.get('numJustificant');
      control?.setValue('');
      control?.markAsTouched();
      
      expect(control?.valid).toBeTruthy();
    });

    it('debe rechazar caracteres no numéricos', () => {
      const control = component.componentForm.get('numJustificant');
      control?.setValue('123abc456');
      control?.markAsTouched();
      
      expect(control?.valid).toBeFalsy();
      expect(control?.errors?.['invalidFormat']).toBeTruthy();
    });

    it('debe permitir menos de 13 números sin error', () => {
      const control = component.componentForm.get('numJustificant');
      control?.setValue('12345');
      control?.markAsTouched();
      
      expect(control?.valid).toBeTruthy();
    });

    it('debe rechazar más de 13 números', () => {
      const control = component.componentForm.get('numJustificant');
      control?.setValue('12345678901234'); // 14 números
      control?.markAsTouched();
      
      expect(control?.valid).toBeFalsy();
      expect(control?.errors?.['invalidLength']).toBeTruthy();
    });

    it('debe rechazar exactamente 13 números que no empiecen por el prefijo correcto', () => {
      const control = component.componentForm.get('numJustificant');
      control?.setValue('9876543210123'); // 13 números pero empieza por 987, no por 123
      control?.markAsTouched();
      
      expect(control?.valid).toBeFalsy();
      expect(control?.errors?.['invalidStart']).toBeTruthy();
    });

    it('debe aceptar exactamente 13 números que empiecen por el prefijo correcto', () => {
      const control = component.componentForm.get('numJustificant');
      control?.setValue('1234567890123'); // 13 números empezando por 123
      control?.markAsTouched();
      
      expect(control?.valid).toBeTruthy();
    });

    it('debe validar cuando el usuario elimina caracteres después de haber introducido 13', () => {
      const control = component.componentForm.get('numJustificant');
      
      // Primero introduce 13 caracteres válidos
      control?.setValue('1234567890123');
      control?.markAsTouched();
      expect(control?.valid).toBeTruthy();
      
      // Luego elimina algunos caracteres
      control?.setValue('123456789012'); // 12 números
      expect(control?.valid).toBeFalsy();
      expect(control?.errors?.['invalidLength']).toBeTruthy();
    });
  });
});
